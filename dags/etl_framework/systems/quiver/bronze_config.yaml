# Configuração Bronze - Sistema Quiver
tables:
  # Tabelas pequenas (sempre full load)
  tabela_clientender:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_clientfones:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_documentos:
    type: large
    mode: full_only
    timeout_seconds: 600
    
  tabela_docsparcscom:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_docsparcsprem:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  Tabela_DocsParcsRep:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  Tabela_DocsItens:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_docsrepasses:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_gruposhierarq:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  tabela_sinistros:
    type: large
    mode: full_only
    timeout_seconds: 600
    
  Tabela_SinistroPagto:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  Tabela_SinistroCober:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  # Tabelas grandes com incremental inteligente
  tabela_clientes:
    type: large
    mode: smart_incremental
    id_field: "Cliente"
    date_field: "Data_alteracao"
    timeout_seconds: 900
    seven_days_timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_divisoes:
    type: small
    mode: smart_incremental
    id_field: "Divisao"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_formasreccom:
    type: small
    mode: smart_incremental
    id_field: "forma_recebimento"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_meiospagto:
    type: small
    mode: smart_incremental
    id_field: "meio_pagto"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_niveishierarq:
    type: small
    mode: smart_incremental
    id_field: "Nivel"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_produtos:
    type: small
    mode: smart_incremental
    id_field: "Produto"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_ramos:
    type: small
    mode: smart_incremental
    id_field: "Ramo"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_seguradoras:
    type: small
    mode: smart_incremental
    id_field: "Seguradora"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_subtiposdoc:
    type: small
    mode: smart_incremental
    id_field: "sub_tipo"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_tiposdocemiss:
    type: small
    mode: smart_incremental
    id_field: "tipo_documento"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  tabela_tiposendereco:
    type: small
    mode: smart_incremental
    id_field: "tipo_endereco"
    date_field: "Data_alteracao"
    timeout_seconds: 300
    seven_days_timeout_seconds: 150
    max_gap_percentage: 10.0
    max_abs_diff: 10