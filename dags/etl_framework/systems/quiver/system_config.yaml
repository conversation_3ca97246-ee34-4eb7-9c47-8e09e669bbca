# Configuração do Sistema Quiver - ETL Framework V4
system:
  name: "quiver"
  description: "Sistema de gestão de seguros - Quiver"
  tags: ["seguros", "financeiro", "sinistros"]
  
  # Configurações globais
  default_timeout_seconds: 900
  max_parallel_tables: 2
  enable_smart_incremental: true
  enable_validation: true
  max_retries: 2
  retry_delay_minutes: 1

# Configuração da base de dados origem
source_database:
  name: "quiver"
  type: "sqlserver"
  host: "${QUIVER_HOST}"
  port: ${QUIVER_PORT}
  database: "${QUIVER_DBNAME}"
  user: "${QUIVER_USERNAME}"
  password: "${QUIVER_PASSWORD}"
  default_timeout: 300
  description: "Banco de dados do sistema Quiver de gestão de seguros"
  
# Configuração da base de dados destino
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_quiver_"
  
# Configurações de performance
performance:
  chunk_size: 50000
  read_chunk_size: 50000
  connection_pool_size: 5
  enable_connection_sharing: false
  enable_lazy_loading: true
  validation_cache_ttl: 3600

# Agendamento
scheduling:
  bronze_schedule: "0 6 * * *"
  silver_schedule: "0 8 * * *"
  timezone: "America/Sao_Paulo"