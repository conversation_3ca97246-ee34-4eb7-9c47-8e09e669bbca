# Configuração Silver - Sistema Quiver
silver_tables:
  faturamento:
    source_query: "quiver_faturamento"
    target_table: "silver_quiver_faturamento"
    schedule_override: "0 */24 * * *"  # 1x ao dia
    timeout_seconds: 1200
    indexes:
      idx_faturamento_data: ["data_faturamento"]
      idx_faturamento_cliente: ["cliente_id"]
    
  clientes:
    source_query: "quiver_clientes"
    target_table: "silver_quiver_clientes"
    schedule_override: "0 */24 * * *"  # 1x ao dia
    timeout_seconds: 900
    indexes:
      idx_clientes_cpf_cnpj: ["cpf_cnpj"]
      idx_clientes_nome: ["nome_cliente"]
    
  financeiro:
    source_query: "quiver_financeiro"
    target_table: "silver_quiver_financeiro"
    schedule_override: "0 */24 * * *"  # 1x ao dia
    timeout_seconds: 1200
    indexes:
      idx_financeiro_vencimento: ["data_vencimento"]
      idx_financeiro_cliente: ["cliente_id"]
      idx_financeiro_status: ["status_pagamento"]

# Configurações globais para camada Silver
silver_config:
  default_timeout_seconds: 900
  enable_indexes: true
  enable_partitioning: false
  partition_field: null
  enable_materialized_views: false
  
# Mapeamento de queries (referência para o sistema de queries)
query_references:
  quiver_faturamento: "Query.Direct_Queries.quiver_faturamento"
  quiver_clientes: "Query.Direct_Queries.quiver_clientes"
  quiver_financeiro: "Query.Direct_Queries.quiver_financeiro"