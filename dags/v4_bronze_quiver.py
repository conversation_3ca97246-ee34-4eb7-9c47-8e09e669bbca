"""
DAG Bronze Quiver - ETL Framework V4
Sistema de carga de dados bronze para o sistema Quiver
"""
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
import sys
import os

# Adiciona o diretório etl_framework ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ''))

# Importações do framework V4
from etl_framework.c1_bronze.bronze_orchestrator import BronzeOrchestrator

# Configurações padrão da DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Instanciação da DAG
dag = DAG(
    'v4_bronze_quiver',
    default_args=default_args,
    description='DAG Bronze para sistema Quiver - Framework V4',
    schedule_interval='0 6 * * *',  # Executa às 6h diariamente
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['bronze', 'quiver', 'v4', 'etl-framework'],
    max_active_runs=1,
)

def execute_bronze_load(**context):
    """Executa o carregamento bronze para o sistema Quiver"""
    
    # Instancia o orquestrador
    orchestrator = BronzeOrchestrator(
        system_name='quiver',
        airflow_context=context
    )
    
    # Executa o processo de carga
    result = orchestrator.execute()
    
    # Retorna o resultado para o XCom
    return {
        'status': result.status,
        'tables_processed': result.tables_processed,
        'tables_failed': result.tables_failed,
        'execution_time': result.execution_time,
        'summary': result.summary
    }

def check_bronze_health(**context):
    """Verifica a saúde do processo bronze"""
    
    orchestrator = BronzeOrchestrator(
        system_name='quiver',
        airflow_context=context
    )
    
    # Verifica a saúde do sistema
    health_status = orchestrator.check_health()
    
    if not health_status['healthy']:
        raise Exception(f"Sistema não está saudável: {health_status['issues']}")
    
    return health_status

# Task de verificação de saúde
health_check_task = PythonOperator(
    task_id='check_bronze_health',
    python_callable=check_bronze_health,
    dag=dag,
)

# Task principal de carga bronze
bronze_load_task = PythonOperator(
    task_id='execute_bronze_load',
    python_callable=execute_bronze_load,
    dag=dag,
)

# Define a ordem de execução
health_check_task >> bronze_load_task

if __name__ == "__main__":
    dag.cli()