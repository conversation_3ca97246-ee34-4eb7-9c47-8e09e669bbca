"""
Sistema Factory - ETL Framework V4

Factory genérica que cria SystemConfig para qualquer sistema baseado apenas em 3 YAMLs:
- system_config.yaml: Conexões, metadados, performance
- bronze_config.yaml: Tabelas bronze
- silver_config.yaml: Transformações silver

Esta abordagem elimina duplicação de código e torna a adição de novos sistemas trivial.
"""

from typing import Dict, Any
from etl_framework.config.system_config import SystemConfig
from etl_framework.config.dw_config import DWConfig
from etl_framework.config.performance_config import get_global_performance_config
from etl_framework.utils.system_config_loader import SystemConfigLoader
from etl_framework.config.table_config import TableConfigLoader


class SystemFactory:
    """
    Factory genérica para criar SystemConfig de qualquer sistema.
    
    Funciona apenas com os 3 arquivos YAML padronizados:
    - system_config.yaml
    - bronze_config.yaml 
    - silver_config.yaml
    
    Elimina a necessidade de criar config.py específico para cada sistema.
    """
    
    def __init__(self):
        self.system_loader = SystemConfigLoader()
    
    def create_system_config(self, system_name: str, dw_config: DWConfig) -> SystemConfig:
        """
        Cria SystemConfig genérico para qualquer sistema.
        
        Args:
            system_name: Nome do sistema (e.g., 'syonet', 'oracle_erp')
            dw_config: Configuração do Data Warehouse
            
        Returns:
            SystemConfig completamente configurado
        """
        # 🚀 Carrega performance global
        perf_config = get_global_performance_config()
        optimized_settings = perf_config.get_optimized_settings()
        
        # 📋 Carrega dados específicos do sistema
        system_config_data = self.system_loader.load_system_config(system_name)
        source_config = self.system_loader.create_source_database_config(system_name)
        schema_mapping = self.system_loader.get_schema_mapping(system_name)
        
        # 🏗️ Cria SystemConfig usando dados do YAML + performance global
        system_config = SystemConfig(
            name=system_config_data.metadata.name,
            description=f"{system_config_data.metadata.description} (V4 Genérica)",
            source_db_config=source_config,
            
            # Performance: Combina configurações específicas + globais
            default_chunk_size=getattr(system_config_data.performance, 'chunk_size', optimized_settings['default_chunk_size']),
            default_timeout=system_config_data.metadata.default_timeout_seconds,
            max_parallel_tables=system_config_data.metadata.max_parallel_tables,
            
            # Configurações do sistema
            enable_smart_incremental=system_config_data.metadata.enable_smart_incremental,
            enable_validation=system_config_data.metadata.enable_validation,
            max_retries=system_config_data.metadata.max_retries,
            retry_delay_minutes=system_config_data.metadata.retry_delay_minutes,
            
            # Performance dinâmica
            enable_production_mode=perf_config.is_production_mode(),
            validation_cache_ttl=getattr(system_config_data.performance, 'validation_cache_ttl', optimized_settings['validation_cache_ttl']),
            enable_connection_sharing=getattr(system_config_data.performance, 'enable_connection_sharing', False),
            enable_lazy_loading=getattr(system_config_data.performance, 'enable_lazy_loading', perf_config.enable_lazy_loading)
        )
        
        # 🥉 Carrega tabelas bronze usando TableConfigLoader com herança
        import os
        from pathlib import Path
        system_path = Path(__file__).parent / system_name
        table_loader = TableConfigLoader(str(system_path))
        bronze_tables_dict = table_loader.load_table_configs('bronze')
        
        for table_config in bronze_tables_dict.values():
            system_config.add_bronze_table(table_config)
        
        # 🥈 Carrega transformações silver (já implementado no SystemConfig)
        transformations_loaded = system_config.load_silver_transformations_from_sql()
        
        # 📊 Log de resultado
        print(f"✅ {system_name.upper()} V4 (Factory): {len(bronze_tables_dict)} tabelas bronze carregadas")
        print(f"✅ {system_name.upper()} V4 (Factory): {transformations_loaded} transformações silver carregadas")
        
        return system_config
    
    def get_system_scheduling_config(self, system_name: str) -> Dict[str, Any]:
        """
        Obtém configurações de agendamento do sistema.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com configurações de agendamento
        """
        system_config_data = self.system_loader.load_system_config(system_name)
        
        return {
            'bronze_schedule': system_config_data.scheduling.bronze_schedule,
            'silver_schedule': system_config_data.scheduling.silver_schedule,
            'timezone': system_config_data.scheduling.timezone
        }
    
    def list_available_systems(self) -> list:
        """
        Lista sistemas disponíveis (que têm os 3 YAMLs).
        
        Returns:
            Lista de nomes de sistemas disponíveis
        """
        # Lista diretórios que têm system_config.yaml
        import os
        from pathlib import Path
        
        systems_path = Path(__file__).parent
        systems = []
        
        for system_dir in systems_path.iterdir():
            if system_dir.is_dir() and (system_dir / "system_config.yaml").exists():
                systems.append(system_dir.name)
        
        return sorted(systems)
    
    def validate_system_structure(self, system_name: str) -> Dict[str, bool]:
        """
        Valida se sistema tem estrutura completa.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com status de cada arquivo
        """
        import os
        from pathlib import Path

        # Detecta automaticamente o caminho correto (funciona tanto local quanto no Docker)
        current_file = Path(__file__)
        etl_framework_dir = current_file.parent.parent  # vai de systems/ para etl_framework/
        systems_path = etl_framework_dir / "systems"
        system_path = systems_path / system_name
        
        return {
            'system_config': (system_path / "system_config.yaml").exists(),
            'bronze_config': (system_path / "bronze_config.yaml").exists(),
            'silver_config': (system_path / "silver_config.yaml").exists(),
            'sql_folder': (system_path / "sql").exists()
        }


# 🏭 Factory functions para compatibilidade

def create_system_config(system_name: str, dw_config: DWConfig) -> SystemConfig:
    """
    Factory function genérica para criar SystemConfig.
    
    Substitui funções específicas como create_syonet_system_config.
    
    Args:
        system_name: Nome do sistema
        dw_config: Configuração do DW
        
    Returns:
        SystemConfig configurado
    """
    factory = SystemFactory()
    return factory.create_system_config(system_name, dw_config)


def get_system_scheduling_info(system_name: str) -> Dict[str, Any]:
    """
    Obtém informações de agendamento para DAG generators.
    
    Args:
        system_name: Nome do sistema
        
    Returns:
        Dict com configurações de agendamento
    """
    factory = SystemFactory()
    return factory.get_system_scheduling_config(system_name)


# 🔧 Manter compatibilidade com função específica do Syonet
def create_syonet_system_config(dw_config: DWConfig) -> SystemConfig:
    """
    COMPATIBILIDADE: Mantém função específica do Syonet.
    
    Internamente usa a factory genérica.
    """
    return create_system_config('syonet', dw_config)