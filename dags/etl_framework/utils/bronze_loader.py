"""
Bronze Loader - ETL Framework V4

Adapter/Facade para manter compatibilidade com código existente.
Delega para TableConfigLoader que implementa herança de configurações.

IMPORTANTE: Este é um wrapper de compatibilidade. 
Para novos códigos, use TableConfigLoader diretamente.
"""

import os
from typing import Dict, List, Optional
from pathlib import Path
from dataclasses import dataclass

# Importa o novo loader que tem herança
from etl_framework.config.table_config import TableConfig, TableConfigLoader

# Para compatibilidade com imports antigos
from etl_framework.config.table_config import TableType, IncrementalMode


@dataclass
class BronzeTablesConfig:
    """Configuração de tabelas bronze (sem dados de conexão)"""
    tables: Dict[str, Dict]


class BronzeLoader:
    """
    Wrapper de compatibilidade para TableConfigLoader.
    
    Mantém a mesma interface pública mas delega para TableConfigLoader
    que implementa herança de configurações corretamente.
    
    DEPRECATED: Use TableConfigLoader diretamente para novos códigos.
    """
    
    def __init__(self, base_path: str = None):
        if base_path is None:
            # Automaticamente detecta o caminho base do framework
            current_dir = Path(__file__).parent.parent
            self.base_path = current_dir / "systems"
        else:
            self.base_path = Path(base_path)
        
        # Armazena loaders por sistema para reutilização
        self._loaders = {}
    
    def load_bronze_tables_config(self, system_name: str) -> BronzeTablesConfig:
        """
        Carrega APENAS configurações de tabelas bronze.
        DEPRECATED: Use TableConfigLoader.load_table_configs() diretamente.
        
        Args:
            system_name: Nome do sistema (ex: 'syonet', 'oracle_erp')
            
        Returns:
            BronzeTablesConfig com configurações de tabelas
        """
        # Carrega via TableConfigLoader para ter herança
        loader = self._get_loader(system_name)
        table_configs = loader.load_table_configs('bronze')
        
        # Converte de volta para formato antigo para compatibilidade
        tables_dict = {}
        for name, config in table_configs.items():
            tables_dict[name] = config.to_dict()
        
        return BronzeTablesConfig(tables=tables_dict)
    
    def _get_loader(self, system_name: str) -> TableConfigLoader:
        """Obtém ou cria TableConfigLoader para o sistema"""
        if system_name not in self._loaders:
            system_path = str(self.base_path / system_name)
            self._loaders[system_name] = TableConfigLoader(system_path)
        return self._loaders[system_name]
    
    def create_table_configs(self, system_name: str, schema_mapping: Dict[str, str] = None) -> List[TableConfig]:
        """
        Cria lista de TableConfig usando informações de schema externa.
        DEPRECATED: Use TableConfigLoader.load_table_configs() diretamente.
        
        Args:
            system_name: Nome do sistema
            schema_mapping: Mapeamento de schemas (ignorado - usa do sistema)
            
        Returns:
            Lista de TableConfig configuradas
        """
        # Usa TableConfigLoader que já aplica herança
        loader = self._get_loader(system_name)
        table_configs = loader.load_table_configs('bronze')
        
        # Converte dict para list para compatibilidade
        return list(table_configs.values())
    
    
    def create_system_tables_from_yaml(self, system_name: str, schema_mapping: Dict[str, str] = None) -> List[TableConfig]:
        """
        Função principal: Carrega todas as tabelas de um sistema do YAML.
        DEPRECATED: Use TableConfigLoader.load_table_configs() diretamente.
        
        Args:
            system_name: Nome do sistema
            schema_mapping: Mapeamento de schemas (ignorado)
            
        Returns:
            Lista de TableConfig prontas para uso
        """
        return self.create_table_configs(system_name, schema_mapping)
    
    def get_available_systems(self) -> List[str]:
        """
        Retorna lista de sistemas disponíveis (que têm bronze_config.yaml)
        
        Returns:
            Lista de nomes de sistemas
        """
        systems = []
        
        if not self.base_path.exists():
            return systems
        
        for system_dir in self.base_path.iterdir():
            if system_dir.is_dir():
                config_file = system_dir / "bronze_config.yaml"
                if config_file.exists():
                    systems.append(system_dir.name)
        
        return sorted(systems)


def create_bronze_loader() -> BronzeLoader:
    """Factory function para criar BronzeLoader"""
    return BronzeLoader()