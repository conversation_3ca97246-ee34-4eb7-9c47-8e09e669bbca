# Configuração Silver - Sistema {NOME}
# Template para transformações silver - ajuste conforme necessário

tables:    
  # ===== TABELA MATERIALIZADA =====
  silver_agregacao:
    type: table
    source_tables:
      - bronze_{sistema}_vendas
      - bronze_{sistema}_produtos
    refresh_mode: "incremental"  # ou "full"
    id_field: "data_venda"  # Para controle incremental
    sql_file: "silver_agregacao.sql"
    
  # ===== CONFIGURAÇÕES OPCIONAIS (use apenas se necessário) =====
  
  # Tabela com índices para performance
  # silver_com_indices:
  #   type: table
  #   indexes:
  #     - ["campo1", "campo2"]
  #     - ["campo3"]
  
  # Tabela que depende de outra silver
  # silver_dependente:
  #   depends_on:
  #     - silver_agregacao

# Ordem de processamento (opcional)
# processing_groups:
#   - group: "base"
#     tables: ["vw_exemplo", "silver_agregacao"]
#   - group: "final"
#     tables: ["silver_dependente"]
#     depends_on: ["base"]