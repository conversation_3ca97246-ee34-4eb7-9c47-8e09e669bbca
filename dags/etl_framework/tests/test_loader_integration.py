"""
Testes de integração entre os loaders refatorados
"""

import sys
import os

# Adiciona o diretório pai (dags) ao path
current_dir = os.path.dirname(os.path.abspath(__file__))
etl_framework_dir = os.path.dirname(current_dir)
dags_dir = os.path.dirname(etl_framework_dir)
sys.path.insert(0, dags_dir)

from etl_framework.utils.system_config_loader import SystemConfigLoader
from etl_framework.utils.bronze_loader import BronzeLoader
from etl_framework.config.table_config import TableConfigLoader


def test_loader_separation():
    """Testa separação de responsabilidades entre loaders"""
    print("🧪 Testando separação de responsabilidades...")
    
    # SystemConfigLoader: apenas configurações de sistema
    system_loader = SystemConfigLoader()
    system_config = system_loader.load_system_config('syonet')
    
    print("\n📊 SystemConfigLoader fornece:")
    print(f"  - Nome do sistema: {system_config.metadata.name}")
    print(f"  - Database config: {system_config.source_database['type']}")
    print(f"  - Schema mapping: {system_loader.get_schema_mapping('syonet')}")
    print(f"  - System defaults: {system_loader.get_system_defaults('syonet')}")
    
    # TableConfigLoader: configurações de tabelas com herança
    table_loader = TableConfigLoader('/home/<USER>/projects/airflow-v1/dags/etl_framework/systems/syonet')
    tables = table_loader.load_table_configs('bronze')
    
    print(f"\n📊 TableConfigLoader carregou {len(tables)} tabelas com herança")
    
    # Testa uma tabela específica
    if 'syo_cliente' in tables:
        cliente = tables['syo_cliente']
        print(f"\n🔍 Exemplo - syo_cliente:")
        print(f"  - Timeout: {cliente.timeout_seconds}s (herdado)")
        print(f"  - Chunk size: {cliente.chunk_size} (herdado)")
        print(f"  - Max gap %: {cliente.max_gap_percentage} (herdado)")
    
    return True


def test_bronze_loader_compatibility():
    """Testa compatibilidade do BronzeLoader refatorado"""
    print("\n🧪 Testando compatibilidade do BronzeLoader...")
    
    # BronzeLoader agora é um wrapper
    bronze_loader = BronzeLoader()
    
    # Método antigo ainda funciona
    tables = bronze_loader.create_table_configs('syonet')
    
    print(f"\n✅ BronzeLoader (wrapper) carregou {len(tables)} tabelas")
    print("  - Mantém interface antiga")
    print("  - Usa TableConfigLoader internamente")
    print("  - Aplica herança corretamente")
    
    # Verifica que herança foi aplicada
    for table in tables[:3]:
        print(f"\n  📋 {table.name}:")
        print(f"     - Chunk size: {table.chunk_size} (deve ser 50000)")
        print(f"     - Type: {table.table_type}")
    
    return True


def test_integration_flow():
    """Testa fluxo completo de integração"""
    print("\n🧪 Testando fluxo completo de integração...")
    
    # 1. SystemConfigLoader fornece configurações de sistema
    system_loader = SystemConfigLoader()
    db_config = system_loader.create_source_database_config('syonet')
    schema_mapping = system_loader.get_schema_mapping('syonet')
    
    print("\n1️⃣ SystemConfigLoader:")
    print(f"   - Database: {db_config.database}")
    print(f"   - Target schema: {schema_mapping['target_schema']}")
    
    # 2. TableConfigLoader usa essas informações
    table_loader = TableConfigLoader('/home/<USER>/projects/airflow-v1/dags/etl_framework/systems/syonet')
    tables = table_loader.load_table_configs('bronze')
    
    print("\n2️⃣ TableConfigLoader:")
    print(f"   - Carregou {len(tables)} tabelas")
    print(f"   - Aplicou herança de 3 níveis")
    
    # 3. Verificação final
    exemplo = tables.get('syo_evento')
    if exemplo:
        print("\n3️⃣ Resultado final (syo_evento):")
        print(f"   - Schema origem: {exemplo.source_schema}")
        print(f"   - Schema destino: {exemplo.target_schema}")
        print(f"   - Prefixo: {exemplo.table_prefix}")
        print(f"   - Timeout: {exemplo.timeout_seconds}s")
    
    return True


def test_no_circular_dependencies():
    """Verifica que não há dependências circulares"""
    print("\n🧪 Verificando dependências...")
    
    try:
        # Importa todos os módulos
        from etl_framework.utils.system_config_loader import SystemConfigLoader
        from etl_framework.utils.bronze_loader import BronzeLoader
        from etl_framework.config.table_config import TableConfigLoader
        
        print("✅ Sem dependências circulares")
        print("  - system_config_loader: configurações de sistema")
        print("  - table_config: herança de configurações")
        print("  - bronze_loader: wrapper de compatibilidade")
        
        return True
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("🔧 TESTE DE INTEGRAÇÃO DOS LOADERS")
    print("=" * 60)
    
    # Executa testes
    tests = [
        test_loader_separation,
        test_bronze_loader_compatibility,
        test_integration_flow,
        test_no_circular_dependencies
    ]
    
    results = []
    for test in tests:
        try:
            success = test()
            results.append(success)
        except Exception as e:
            print(f"\n❌ Erro no teste {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    if all(results):
        print("✅ TODOS OS TESTES DE INTEGRAÇÃO PASSARAM!")
        print("\n📋 Resumo da arquitetura:")
        print("1. SystemConfigLoader: Configurações de sistema/conexão")
        print("2. TableConfigLoader: Configurações de tabelas com herança") 
        print("3. BronzeLoader: Wrapper de compatibilidade (deprecated)")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
    print("=" * 60)