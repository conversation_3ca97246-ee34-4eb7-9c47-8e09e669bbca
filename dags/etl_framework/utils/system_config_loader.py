"""
SystemConfigLoader - ETL Framework V4

Carrega configurações de sistema a partir de system_config.yaml.
Especializado em conexões de banco, metadados e configurações globais.

RESPONSABILIDADES:
- Carregar system_config.yaml
- Fornecer configurações de conexão (DatabaseConfig)
- Fornecer metadados do sistema
- Fornecer configurações de performance e scheduling

NÃO FAZ:
- Não carrega configurações de tabelas (use TableConfigLoader)
- Não aplica herança de configurações de tabelas
"""

import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

try:
    from etl_framework.config.database_config import DatabaseConfig, DatabaseType
except ImportError:
    # Para compatibilidade quando database_config não existe
    from dataclasses import dataclass as DatabaseConfig
    from enum import Enum
    class DatabaseType(Enum):
        SQLSERVER = "sqlserver"
        ORACLE = "oracle"
        POSTGRESQL = "postgresql"


@dataclass
class SystemMetadata:
    """Metadados do sistema"""
    name: str
    description: str
    tags: list
    default_timeout_seconds: int
    max_parallel_tables: int
    enable_smart_incremental: bool
    enable_validation: bool
    max_retries: int
    retry_delay_minutes: int


@dataclass
class PerformanceConfig:
    """Configurações de performance"""
    chunk_size: int
    connection_pool_size: int
    enable_connection_sharing: bool
    enable_lazy_loading: bool
    validation_cache_ttl: int


@dataclass
class ETLSettings:
    """Configurações de ETL específicas"""
    default_incremental_filters: Dict[str, str]
    default_validation: Dict[str, Any]


@dataclass
class SchedulingConfig:
    """Configurações de agendamento"""
    bronze_schedule: str
    silver_schedule: str
    timezone: str


@dataclass
class SystemConfigData:
    """Dados completos de configuração do sistema"""
    metadata: SystemMetadata
    source_database: Dict[str, Any]
    target_database: Dict[str, str]
    performance: PerformanceConfig
    etl_settings: ETLSettings
    scheduling: SchedulingConfig


class SystemConfigLoader:
    """
    Loader especializado para configurações de sistema.
    
    Responsável por:
    - Carregar system_config.yaml
    - Criar DatabaseConfig para conexões
    - Fornecer metadados e configurações globais
    - Manter separação clara de responsabilidades
    """
    
    def __init__(self):
        # Detecta automaticamente o caminho correto (funciona tanto local quanto no Docker)
        import os
        from pathlib import Path

        # Tenta encontrar o diretório etl_framework
        current_file = Path(__file__)
        etl_framework_dir = current_file.parent.parent  # vai de utils/ para etl_framework/
        systems_path = etl_framework_dir / "systems"

        self.systems_path = str(systems_path)
    
    def load_system_config(self, system_name: str) -> SystemConfigData:
        """
        Carrega configuração completa do sistema.
        
        Args:
            system_name: Nome do sistema (e.g., 'syonet')
            
        Returns:
            SystemConfigData com todas as configurações
        """
        config_path = os.path.join(self.systems_path, system_name, "system_config.yaml")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"system_config.yaml não encontrado para sistema '{system_name}' em {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as file:
            config_data = yaml.safe_load(file)
        
        return self._parse_system_config(config_data)
    
    def create_source_database_config(self, system_name: str) -> DatabaseConfig:
        """
        Cria DatabaseConfig para a base de dados origem.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            DatabaseConfig configurado para a origem
        """
        system_config = self.load_system_config(system_name)
        db_config = system_config.source_database
        
        # Mapear string para enum
        db_type_map = {
            'sqlserver': DatabaseType.SQLSERVER,
            'oracle': DatabaseType.ORACLE,
            'postgresql': DatabaseType.POSTGRESQL
        }
        
        return DatabaseConfig(
            name=db_config.get('name', f"{system_name}_source"),  # 🔧 OPCIONAL: gera nome automático
            db_type=db_type_map[db_config['type'].lower()],
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['database'],
            user=db_config['user'],
            password=db_config['password'],
            timeout=db_config.get('default_timeout', 300)
        )
    
    def get_system_defaults(self, system_name: str) -> Dict[str, Any]:
        """
        Obtém valores padrão do sistema para serem usados pelo TableConfigLoader.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com valores padrão do sistema
        """
        system_config = self.load_system_config(system_name)
        
        defaults = {}
        
        # Timeout padrão do sistema
        if hasattr(system_config.metadata, 'default_timeout_seconds'):
            defaults['timeout_seconds'] = system_config.metadata.default_timeout_seconds
            
        # Performance defaults
        if hasattr(system_config, 'performance'):
            defaults['chunk_size'] = system_config.performance.chunk_size
            
        # Validation defaults
        if hasattr(system_config.etl_settings, 'default_validation'):
            defaults.update(system_config.etl_settings.default_validation)
            
        return defaults
    
    def get_schema_mapping(self, system_name: str) -> Dict[str, str]:
        """
        Obtém mapeamento de schemas entre origem e destino.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com mapeamento de schemas
        """
        system_config = self.load_system_config(system_name)
        source_db = system_config.source_database
        
        return {
            'source_schema': source_db.get('default_schema', 'dbo'),
            'openquery_schema': source_db.get('openquery_schema', 'public'),
            'target_schema': system_config.target_database['schema'],
            'table_prefix': system_config.target_database['table_prefix']
        }
    
    def _parse_system_config(self, config_data: Dict[str, Any]) -> SystemConfigData:
        """
        Parse dos dados do YAML para dataclasses.
        
        Args:
            config_data: Dados do YAML
            
        Returns:
            SystemConfigData parseado
        """
        system_data = config_data.get('system', {})

        # Parse metadata - 🔧 TODOS OS CAMPOS OPCIONAIS COM PADRÕES
        metadata = SystemMetadata(
            name=system_data.get('name', 'unknown_system'),  # 🔧 OPCIONAL
            description=system_data.get('description', 'Sistema sem descrição'),  # 🔧 OPCIONAL
            tags=system_data.get('tags', []),  # 🔧 OPCIONAL: tags padrão vazio
            default_timeout_seconds=system_data.get('default_timeout_seconds', 900),  # 🔧 OPCIONAL
            max_parallel_tables=system_data.get('max_parallel_tables', 2),  # 🔧 OPCIONAL
            enable_smart_incremental=system_data.get('enable_smart_incremental', True),  # 🔧 OPCIONAL
            enable_validation=system_data.get('enable_validation', True),  # 🔧 OPCIONAL
            max_retries=system_data.get('max_retries', 2),  # 🔧 OPCIONAL
            retry_delay_minutes=system_data.get('retry_delay_minutes', 1)  # 🔧 OPCIONAL
        )
        
        # Parse performance - 🔧 TODOS OS CAMPOS OPCIONAIS COM PADRÕES
        perf_data = config_data.get('performance', {})
        performance = PerformanceConfig(
            chunk_size=perf_data.get('chunk_size', 50000),  # 🔧 OPCIONAL
            connection_pool_size=perf_data.get('connection_pool_size', 5),  # 🔧 OPCIONAL
            enable_connection_sharing=perf_data.get('enable_connection_sharing', False),  # 🔧 OPCIONAL
            enable_lazy_loading=perf_data.get('enable_lazy_loading', True),  # 🔧 OPCIONAL
            validation_cache_ttl=perf_data.get('validation_cache_ttl', 3600)  # 🔧 OPCIONAL
        )
        
        # Parse ETL settings - 🔧 TODOS OS CAMPOS OPCIONAIS COM PADRÕES
        etl_data = config_data.get('etl_settings', {})
        etl_settings = ETLSettings(
            default_incremental_filters=etl_data.get('default_incremental_filters', {}),  # 🔧 OPCIONAL
            default_validation=etl_data.get('default_validation', {})  # 🔧 OPCIONAL
        )
        
        # Parse scheduling - 🔧 TODOS OS CAMPOS OPCIONAIS COM PADRÕES
        sched_data = config_data.get('scheduling', {})
        scheduling = SchedulingConfig(
            bronze_schedule=sched_data.get('bronze_schedule', '0 6 * * *'),  # 🔧 OPCIONAL: 6h da manhã
            silver_schedule=sched_data.get('silver_schedule', '0 8 * * *'),  # 🔧 OPCIONAL: 8h da manhã
            timezone=sched_data.get('timezone', 'America/Sao_Paulo')  # 🔧 OPCIONAL
        )
        
        return SystemConfigData(
            metadata=metadata,
            source_database=config_data['source_database'],
            target_database=config_data['target_database'],
            performance=performance,
            etl_settings=etl_settings,
            scheduling=scheduling
        )


def create_system_config_loader() -> SystemConfigLoader:
    """Factory function para criar SystemConfigLoader"""
    return SystemConfigLoader()